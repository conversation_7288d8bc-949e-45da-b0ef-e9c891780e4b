import 'package:hive/hive.dart';

part 'chapter.g.dart';

/// 章节类型枚举
@HiveType(typeId: 3)
enum ChapterType {
  @HiveField(0)
  outline, // 大纲章节（长篇小说的细纲）

  @HiveField(1)
  worldBuilding, // 世界观章节（短篇小说的世界观设定）

  @HiveField(2)
  content, // 正文章节
}

@HiveType(typeId: 2)
class Chapter {
  @HiveField(0)
  final int number;

  @HiveField(1)
  final String title;

  @HiveField(2)
  final String content;

  @HiveField(3)
  final String createTime;

  @HiveField(4)
  final String? id;

  @HiveField(5)
  final bool isSelected;

  @HiveField(6)
  final ChapterType? chapterType;
  
  Chapter({
    required this.number,
    required this.title,
    required this.content,
    String? createTime,
    this.id,
    this.isSelected = false,
    this.chapterType,
  }) : createTime = createTime ?? DateTime.now().toString();
  
  Chapter copyWith({
    int? number,
    String? title,
    String? content,
    String? createTime,
    String? id,
    bool? isSelected,
    ChapterType? chapterType,
  }) {
    return Chapter(
      number: number ?? this.number,
      title: title ?? this.title,
      content: content ?? this.content,
      createTime: createTime ?? this.createTime,
      id: id ?? this.id,
      isSelected: isSelected ?? this.isSelected,
      chapterType: chapterType ?? this.chapterType,
    );
  }
  
  int get wordCount {
    return content.length;
  }

  /// 是否为大纲章节（长篇小说的细纲）
  bool get isOutline => chapterType == ChapterType.outline;

  /// 是否为世界观章节（短篇小说的世界观设定）
  bool get isWorldBuilding => chapterType == ChapterType.worldBuilding;

  /// 是否为正文章节
  bool get isContent => chapterType == ChapterType.content || chapterType == null;

  /// 是否为特殊章节（大纲或世界观）
  bool get isSpecialChapter => isOutline || isWorldBuilding;

  /// 获取章节类型的显示名称
  String get chapterTypeDisplayName {
    switch (chapterType) {
      case ChapterType.outline:
        return '细纲';
      case ChapterType.worldBuilding:
        return '世界观';
      case ChapterType.content:
      case null:
        return '正文';
    }
  }

  /// 获取章节索引（从0开始）
  int get index => number - 1;

  /// 转换为JSON
  Map<String, dynamic> toJson() => {
        'number': number,
        'title': title,
        'content': content,
        'createTime': createTime,
        'id': id,
        'isSelected': isSelected,
        'chapterType': chapterType?.name,
      };

  /// 从JSON创建章节
  factory Chapter.fromJson(Map<String, dynamic> json) => Chapter(
        number: json['number'] as int? ?? 1,
        title: json['title'] as String? ?? '未命名章节',
        content: json['content'] as String? ?? '',
        createTime: json['createTime'] as String?,
        id: json['id'] as String?,
        isSelected: json['isSelected'] as bool? ?? false,
        chapterType: json['chapterType'] != null
            ? ChapterType.values.firstWhere(
                (e) => e.name == json['chapterType'],
                orElse: () => ChapterType.content,
              )
            : null,
      );

  @override
  String toString() {
    return 'Chapter{number: $number, title: $title, type: $chapterTypeDisplayName, wordCount: $wordCount}';
  }
} 