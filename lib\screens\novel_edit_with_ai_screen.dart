import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/smart_composer_controller.dart';
import '../controllers/novel_controller.dart';
import '../controllers/api_config_controller.dart';
import '../models/novel.dart';
import '../models/chapter.dart';
import '../models/smart_composer_models.dart';

/// 带有AI助手的小说编辑界面
/// 类似于Cursor的体验，左侧编辑器，右侧AI聊天框
class NovelEditWithAIScreen extends StatefulWidget {
  final Novel novel;
  final int? initialChapterIndex;

  const NovelEditWithAIScreen({
    super.key,
    required this.novel,
    this.initialChapterIndex,
  });

  @override
  State<NovelEditWithAIScreen> createState() => _NovelEditWithAIScreenState();
}

class _NovelEditWithAIScreenState extends State<NovelEditWithAIScreen> {
  final SmartComposerController _smartComposerController = Get.find<SmartComposerController>();
  final NovelController _novelController = Get.find<NovelController>();
  final ApiConfigController _apiConfig = Get.find<ApiConfigController>();
  
  final TextEditingController _titleController = TextEditingController();
  final List<TextEditingController> _chapterControllers = [];
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _chatScrollController = ScrollController();
  final ScrollController _editorScrollController = ScrollController();
  
  late Novel _currentNovel;
  int _currentChapterIndex = 0;
  bool _hasChanges = false;
  bool _showAIPanel = true;
  double _aiPanelWidth = 400;
  ChatSession? _currentSession;
  
  @override
  void initState() {
    super.initState();
    _currentNovel = widget.novel.copyWith();
    _currentChapterIndex = widget.initialChapterIndex ?? 0;
    _titleController.text = _currentNovel.title;
    _initChapterControllers();
    _initAISession();
    
    _titleController.addListener(_onTextChanged);
  }

  void _initChapterControllers() {
    _chapterControllers.clear();
    for (final chapter in _currentNovel.chapters) {
      final controller = TextEditingController(text: chapter.content);
      controller.addListener(_onTextChanged);
      _chapterControllers.add(controller);
    }
  }

  void _initAISession() {
    _currentSession = _smartComposerController.createNewSession(
      title: '《${_currentNovel.title}》编辑助手',
      novelId: _currentNovel.id,
      chapterNumber: _currentChapterIndex + 1,
      context: {
        'novel_title': _currentNovel.title,
        'novel_genre': _currentNovel.genre,
        'novel_outline': _currentNovel.outline,
        'chapter_count': _currentNovel.chapters.length,
        'current_chapter': _currentChapterIndex + 1,
      },
    );
  }

  void _onTextChanged() {
    if (!_hasChanges) {
      setState(() {
        _hasChanges = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: TextField(
          controller: _titleController,
          style: const TextStyle(color: Colors.white),
          decoration: const InputDecoration(
            border: InputBorder.none,
            hintText: '输入小说标题',
            hintStyle: TextStyle(color: Colors.white70),
          ),
        ),
        actions: [
          IconButton(
            icon: Icon(_showAIPanel ? Icons.close_fullscreen : Icons.open_in_full),
            tooltip: _showAIPanel ? '隐藏AI助手' : '显示AI助手',
            onPressed: () {
              setState(() {
                _showAIPanel = !_showAIPanel;
              });
            },
          ),
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _hasChanges ? _saveChanges : null,
            tooltip: '保存',
          ),
        ],
      ),
      body: Row(
        children: [
          // 左侧编辑器
          Expanded(
            flex: _showAIPanel ? 3 : 1,
            child: _buildEditor(),
          ),
          
          // 右侧AI助手面板
          if (_showAIPanel) ...[
            Container(
              width: 1,
              color: Theme.of(context).dividerColor,
            ),
            SizedBox(
              width: _aiPanelWidth,
              child: _buildAIPanel(),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildEditor() {
    return Column(
      children: [
        // 章节选择器
        if (_currentNovel.chapters.isNotEmpty) _buildChapterSelector(),
        
        // 编辑器内容
        Expanded(
          child: Container(
            padding: const EdgeInsets.all(16),
            child: _buildEditorContent(),
          ),
        ),
      ],
    );
  }

  Widget _buildChapterSelector() {
    return Container(
      height: 50,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        children: [
          const Text('章节：'),
          const SizedBox(width: 8),
          Expanded(
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _currentNovel.chapters.length,
              itemBuilder: (context, index) {
                return Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: ChoiceChip(
                    label: Text('第${_currentNovel.chapters[index].number}章'),
                    selected: _currentChapterIndex == index,
                    onSelected: (selected) {
                      if (selected) {
                        _switchToChapter(index);
                      }
                    },
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEditorContent() {
    if (_currentNovel.chapters.isEmpty) {
      return _buildShortNovelEditor();
    }

    if (_currentChapterIndex >= _currentNovel.chapters.length) {
      return const Center(child: Text('暂无内容'));
    }

    final chapter = _currentNovel.chapters[_currentChapterIndex];
    
    return SingleChildScrollView(
      controller: _editorScrollController,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            chapter.title,
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _chapterControllers[_currentChapterIndex],
            maxLines: null,
            minLines: 20,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              hintText: '在此编写章节内容...',
              contentPadding: EdgeInsets.all(16),
            ),
            style: const TextStyle(
              fontSize: 16,
              height: 1.8,
            ),
            // TextField 不支持 onSelectionChanged，我们可以通过其他方式处理文本选择
          ),
        ],
      ),
    );
  }

  Widget _buildShortNovelEditor() {
    final contentController = TextEditingController(text: _currentNovel.content);
    contentController.addListener(_onTextChanged);
    
    return SingleChildScrollView(
      controller: _editorScrollController,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            _currentNovel.title,
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: contentController,
            maxLines: null,
            minLines: 20,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              hintText: '在此编写小说内容...',
              contentPadding: EdgeInsets.all(16),
            ),
            style: const TextStyle(
              fontSize: 16,
              height: 1.8,
            ),
            onChanged: (value) {
              _currentNovel = _currentNovel.copyWith(content: value);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildAIPanel() {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          left: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 0.5,
          ),
        ),
      ),
      child: Column(
        children: [
          // AI面板标题栏
          _buildAIPanelHeader(),
          
          // 聊天消息列表
          Expanded(
            child: _buildChatMessages(),
          ),
          
          // 输入框
          _buildChatInput(),
        ],
      ),
    );
  }

  Widget _buildAIPanelHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.smart_toy,
            color: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(width: 8),
          const Expanded(
            child: Text(
              'AI 写作助手',
              style: TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Obx(() {
            final defaultModel = _smartComposerController.defaultModel;
            if (defaultModel != null) {
              return Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  defaultModel.id,
                  style: TextStyle(
                    fontSize: 10,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              );
            }
            return const SizedBox.shrink();
          }),
        ],
      ),
    );
  }

  Widget _buildChatMessages() {
    return Obx(() {
      final session = _smartComposerController.currentSession.value;
      if (session == null || session.messages.isEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.chat,
                size: 48,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 16),
              Text(
                '开始与AI对话',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                '我可以帮助您：\n• 完善情节发展\n• 优化文字表达\n• 提供创作建议',
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Colors.grey[500],
                  fontSize: 12,
                ),
              ),
            ],
          ),
        );
      }

      return ListView.builder(
        controller: _chatScrollController,
        padding: const EdgeInsets.all(16),
        itemCount: session.messages.length,
        itemBuilder: (context, index) {
          final message = session.messages[index];
          return _buildMessageBubble(message);
        },
      );
    });
  }

  Widget _buildMessageBubble(ChatMessage message) {
    final isUser = message.role == 'user';
    
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Row(
        mainAxisAlignment: isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!isUser) ...[
            CircleAvatar(
              radius: 12,
              backgroundColor: Theme.of(context).colorScheme.primary,
              child: const Icon(
                Icons.smart_toy,
                size: 12,
                color: Colors.white,
              ),
            ),
            const SizedBox(width: 8),
          ],
          Flexible(
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: isUser
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(context).colorScheme.surfaceVariant,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                message.content,
                style: TextStyle(
                  fontSize: 14,
                  color: isUser
                      ? Theme.of(context).colorScheme.onPrimary
                      : Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
            ),
          ),
          if (isUser) ...[
            const SizedBox(width: 8),
            CircleAvatar(
              radius: 12,
              backgroundColor: Theme.of(context).colorScheme.secondary,
              child: const Icon(
                Icons.person,
                size: 12,
                color: Colors.white,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildChatInput() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _messageController,
              maxLines: null,
              decoration: InputDecoration(
                hintText: '询问AI助手...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
              ),
              onSubmitted: (_) => _sendMessage(),
            ),
          ),
          const SizedBox(width: 8),
          Obx(() => IconButton(
                onPressed: _smartComposerController.isLoading.value ? null : _sendMessage,
                icon: _smartComposerController.isLoading.value
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Icon(Icons.send),
              )),
        ],
      ),
    );
  }

  void _switchToChapter(int index) {
    setState(() {
      _currentChapterIndex = index;
    });
    
    // 更新AI会话上下文
    if (_currentSession != null) {
      _currentSession = _currentSession!.copyWith(
        chapterNumber: index + 1,
        context: {
          ..._currentSession!.context ?? {},
          'current_chapter': index + 1,
        },
      );
    }
  }

  void _handleTextSelection(String selectedText) {
    // 当用户选择文本时，可以自动发送相关问题给AI
    if (selectedText.isNotEmpty && selectedText.length < 200) {
      _messageController.text = '请帮我优化这段文字："$selectedText"';
    }
  }

  void _sendMessage() {
    final text = _messageController.text.trim();
    if (text.isEmpty) return;

    _messageController.clear();
    
    // 获取当前章节作为上下文
    Chapter? currentChapter;
    if (_currentNovel.chapters.isNotEmpty && _currentChapterIndex < _currentNovel.chapters.length) {
      currentChapter = _currentNovel.chapters[_currentChapterIndex];
    }
    
    _smartComposerController.sendMessage(
      content: text,
      novel: _currentNovel,
      chapter: currentChapter,
    );

    // 滚动到底部
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_chatScrollController.hasClients) {
        _chatScrollController.animateTo(
          _chatScrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  void _saveChanges() {
    // 更新小说标题
    _currentNovel = _currentNovel.copyWith(title: _titleController.text);
    
    // 更新章节内容
    final updatedChapters = <Chapter>[];
    for (int i = 0; i < _currentNovel.chapters.length; i++) {
      if (i < _chapterControllers.length) {
        final updatedChapter = _currentNovel.chapters[i].copyWith(
          content: _chapterControllers[i].text,
        );
        updatedChapters.add(updatedChapter);
      } else {
        updatedChapters.add(_currentNovel.chapters[i]);
      }
    }
    _currentNovel = _currentNovel.copyWith(chapters: updatedChapters);
    
    // 保存到控制器
    _novelController.saveNovel(_currentNovel);
    
    setState(() {
      _hasChanges = false;
    });
    
    Get.snackbar('成功', '小说已保存');
  }

  @override
  void dispose() {
    _titleController.dispose();
    for (final controller in _chapterControllers) {
      controller.dispose();
    }
    _messageController.dispose();
    _chatScrollController.dispose();
    _editorScrollController.dispose();
    super.dispose();
  }
}
