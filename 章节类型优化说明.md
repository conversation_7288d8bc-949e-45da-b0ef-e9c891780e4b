# 章节类型优化说明

## 问题描述

在之前的实现中，小说的细纲统一保存到第0章，但是当进行数据同步后，第0章会变成第一章，然后第一章变成第二章，导致章节编号混乱。

## 解决方案

为了解决这个问题，我们为章节模型添加了章节类型标志，用于区分不同类型的章节：

### 1. 新增章节类型枚举

```dart
enum ChapterType {
  outline,      // 大纲章节（长篇小说的细纲）
  worldBuilding, // 世界观章节（短篇小说的世界观设定）
  content,      // 正文章节
}
```

### 2. 修改Chapter模型

- 添加了 `chapterType` 字段来标识章节类型
- 添加了便利方法来判断章节类型：
  - `isOutline`: 是否为大纲章节
  - `isWorldBuilding`: 是否为世界观章节
  - `isContent`: 是否为正文章节
  - `isSpecialChapter`: 是否为特殊章节（大纲或世界观）
  - `chapterTypeDisplayName`: 获取章节类型的显示名称

### 3. 修改数据同步逻辑

#### 前端修改
- 在章节重新编号时，跳过特殊章节（细纲、世界观），只对正文章节重新编号
- 特殊章节保持原有编号不变

#### 后端修改
- 在数据同步时，检查章节类型
- 特殊章节保持原有编号，不重新分配
- 正文章节按顺序分配编号
- 排序时，特殊章节排在前面，然后是正文章节

### 4. 修改章节创建逻辑

- 细纲章节：设置 `chapterType: ChapterType.outline`
- 世界观章节：设置 `chapterType: ChapterType.worldBuilding`
- 正文章节：设置 `chapterType: ChapterType.content`

## 修改的文件

### 模型文件
- `lib/models/chapter.dart`: 添加章节类型枚举和相关方法
- `lib/models/novel.dart`: 删除重复的Chapter类定义，导入chapter.dart

### 控制器文件
- `lib/controllers/novel_controller.dart`: 修改章节创建逻辑，设置正确的章节类型
- `lib/controllers/ai_writing_controller.dart`: 修改细纲章节判断逻辑
- `lib/screens/daizong_ai_assistant_screen.dart`: 修改章节重新编号逻辑

### 后端文件
- `cloudbase/cloudbase-deploy/novel-app-api/index.js`: 修改数据同步逻辑
- `cloudbase/cloudbase-deploy/novel-app-api/functions/novel-app-api/index.js`: 修改数据同步逻辑

### 配置文件
- `lib/main.dart`: 注册ChapterType适配器

## 兼容性

- 对于没有章节类型的旧数据，默认被视为正文章节
- 第0章仍然被识别为特殊章节（向后兼容）
- 标题包含"细纲"、"大纲"、"世界观"等关键词的章节也会被识别为特殊章节

## 效果

1. **数据同步后章节编号不再混乱**：特殊章节（细纲、世界观）保持原有编号，正文章节按顺序编号
2. **明确的章节类型区分**：可以清楚地区分细纲、世界观和正文章节
3. **更好的排序逻辑**：特殊章节始终排在前面，便于用户查看
4. **向后兼容**：旧数据仍然可以正常工作

## 使用示例

```dart
// 创建细纲章节
final outlineChapter = Chapter(
  number: 0,
  title: '细纲',
  content: '小说的详细细纲...',
  chapterType: ChapterType.outline,
);

// 创建世界观章节
final worldBuildingChapter = Chapter(
  number: 0,
  title: '世界观与大纲',
  content: '短篇小说的世界观设定...',
  chapterType: ChapterType.worldBuilding,
);

// 创建正文章节
final contentChapter = Chapter(
  number: 1,
  title: '第一章：开始',
  content: '正文内容...',
  chapterType: ChapterType.content,
);

// 判断章节类型
if (chapter.isSpecialChapter) {
  // 这是特殊章节，不参与重新编号
  print('特殊章节: ${chapter.chapterTypeDisplayName}');
} else {
  // 这是正文章节，可以重新编号
  print('正文章节');
}
```

这个优化解决了数据同步时章节编号混乱的问题，同时为不同类型的章节提供了明确的标识和处理逻辑。
